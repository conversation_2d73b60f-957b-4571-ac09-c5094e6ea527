#!/usr/bin/env python3
"""
测试脚本：验证 ollama_think 参数是否正确传递
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:1113"

def test_ollama_think_true():
    """测试 ollama_think=True"""
    print("测试 ollama_think=True...")
    
    payload = {
        "messages": [
            {
                "type": "text",
                "content": "你好，请简单介绍一下你自己",
                "role": "user"
            }
        ],
        "model_name": "qwen3:latest",
        "model_type": "ollama",
        "ollama_think": True,
        "stream": False
    }
    
    try:
        response = requests.post(f"{BASE_URL}/chat", json=payload, timeout=30)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应类型: {result.get('type')}")
            print(f"响应内容: {result.get('content')[:100]}...")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_ollama_think_false():
    """测试 ollama_think=False"""
    print("\n测试 ollama_think=False...")
    
    payload = {
        "messages": [
            {
                "type": "text",
                "content": "什么是人工智能？",
                "role": "user"
            }
        ],
        "model_name": "qwen3:latest",
        "model_type": "ollama",
        "ollama_think": False,
        "stream": False
    }
    
    try:
        response = requests.post(f"{BASE_URL}/chat", json=payload, timeout=30)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应类型: {result.get('type')}")
            print(f"响应内容: {result.get('content')[:100]}...")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_ollama_stream_think_true():
    """测试流式响应 ollama_think=True"""
    print("\n测试流式响应 ollama_think=True...")
    
    payload = {
        "messages": [
            {
                "type": "text",
                "content": "写一个简单的Python函数",
                "role": "user"
            }
        ],
        "model_name": "qwen3:latest",
        "model_type": "ollama",
        "ollama_think": True,
        "stream": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/chat/stream", json=payload, stream=True, timeout=30)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("流式响应内容:")
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        try:
                            data = json.loads(line[6:])
                            if data.get('type') == 'end':
                                break
                            print(f"  {data}")
                        except json.JSONDecodeError:
                            print(f"  无法解析: {line}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    print("开始测试 ollama_think 参数...")
    print("=" * 50)
    
    # 测试健康检查
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code == 200:
            print("✓ 服务健康检查通过")
        else:
            print("✗ 服务健康检查失败")
            exit(1)
    except Exception as e:
        print(f"✗ 无法连接到服务: {e}")
        exit(1)
    
    # 执行测试
    test_ollama_think_true()
    test_ollama_think_false()
    test_ollama_stream_think_true()
    
    print("\n" + "=" * 50)
    print("测试完成！")
