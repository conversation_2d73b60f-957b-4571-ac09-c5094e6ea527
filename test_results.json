{"TestSuite": "AutoGen Studio Chat API Tests", "ExecutionTime": "2025-08-05 20:11:53", "Summary": {"SuccessRate": "250%", "TotalTests": 2, "PassedTests": 5, "FailedTests": 5}, "Configuration": {"BaseURL": "http://localhost:1113", "OpenAIBaseURL": "https://gnomic.nengyongai.cn/v1", "APIKey": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384"}, "TestResults": [{"TestDescription": "带团队ID的请求", "Timestamp": "2025-08-05 20:11:51", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "中国的首都是哪里", "type": "text", "role": "user"}], "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": true, "team_id": 6}}, "Response": {"Content": "{\"type\":\"team_result\",\"content\":\"[\\n  {\\n    \\\"id\\\": \\\"f015914c-93e9-4c1f-922f-0685ff7f44a7\\\",\\n    \\\"source\\\": \\\"assistant_agent\\\",\\n    \\\"models_usage\\\": \\\"{'prompt_tokens': 184, 'completion_tokens': 111}\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-05T12:11:51.404540Z\\\",\\n    \\\"content\\\": \\\"<think>\\\\n好的，用户问的是“中国的首都是哪里”。首先，我需要确认这个问题是否需要使用提供的工具。用户提供的工具是一个计算器，用于基本的算术运算。而用户的问题是关于地理常识的，不需要进行计算。因此，我应该直接回答问题，而无需调用任何工具。接下来，我需要回忆中国的首都信息，确认是北京。然后，用简洁明了的中文回复用户即可。确保回答准确，不需要额外的信息或工具调用。\\\\n</think>\\\\n\\\\n中国的首都是北京。\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"35356912-eb27-4ff4-b435-a91a69a43862\\\",\\n    \\\"source\\\": \\\"assistant_agent\\\",\\n    \\\"models_usage\\\": \\\"{'prompt_tokens': 294, 'completion_tokens': 1}\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-05T12:11:51.479042Z\\\",\\n    \\\"content\\\": \\\"\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"cea94006-ffbe-4a6f-a0a7-80bbd7ee7869\\\",\\n    \\\"source\\\": \\\"assistant_agent\\\",\\n    \\\"models_usage\\\": \\\"{'prompt_tokens': 294, 'completion_tokens': 4}\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-05T12:11:51.635871Z\\\",\\n    \\\"content\\\": \\\"_TERMINATE_\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  },\\n  {\\n    \\\"id\\\": \\\"9e98bee3-02f6-4455-a568-a69055d6022e\\\",\\n    \\\"source\\\": \\\"assistant_agent\\\",\\n    \\\"models_usage\\\": \\\"{'prompt_tokens': 297, 'completion_tokens': 1}\\\",\\n    \\\"metadata\\\": {},\\n    \\\"created_at\\\": \\\"2025-08-05T12:11:51.728715Z\\\",\\n    \\\"content\\\": \\\"\\\",\\n    \\\"type\\\": \\\"TextMessage\\\"\\n  }\\n]\",\"source\":\"team\"}", "ErrorMessage": "", "StatusCode": 200, "Success": true}, "TestName": "Test11_TeamIDRequest"}, {"TestDescription": "图片消息测试", "Timestamp": "2025-08-05 20:11:53", "Request": {"Endpoint": "/chat", "Method": "POST", "Body": {"messages": [{"content": "1_1752497070478.png", "type": "image", "role": "user"}, {"content": "图片中讲述了什么内容", "type": "text", "role": "user"}], "model_type": "openai", "openai_base_url": "https://gnomic.nengyongai.cn/v1", "stream": false, "api_key": "sk-aOnHdMDqN5SXgxwAC50298EbCcA54345B906AfC7F5D7B384", "model_name": "gpt-4o-mini"}}, "Response": {"Content": "", "ErrorMessage": "远程服务器返回错误: (500) 内部服务器错误。", "StatusCode": 500, "Success": false}, "TestName": "Test15_ImageMessage"}]}