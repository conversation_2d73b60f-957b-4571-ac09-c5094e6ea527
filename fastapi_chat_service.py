"""
FastAPI service wrapper for chat_backend function
Provides REST API endpoints for chat functionality
"""

import asyncio
import json
import logging
import traceback
import sys
import os
from typing import List, Optional, Dict, Any, Union
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
try:
    from .chat_backend import chat_backend
except ImportError:
    from chat_backend import chat_backend

# ==================== 配置变量 ====================
# 控制显示信息详略程度的配置变量
VERBOSE_MODE = False #os.getenv("VERBOSE_MODE", "true").lower() == "true"  # 详细模式开关
SHOW_TRACEBACK = 0 #os.getenv("SHOW_TRACEBACK", "true").lower() == "true"  # 显示错误堆栈开关
SHOW_REQUEST_DETAILS = 0 #os.getenv("SHOW_REQUEST_DETAILS", "true").lower() == "true"  # 显示请求详情开关
SHOW_RESPONSE_DETAILS = 0 #os.getenv("SHOW_RESPONSE_DETAILS", "false").lower() == "true"  # 显示响应详情开关
LOG_LEVEL = os.getenv("LOG_LEVEL", "DEBUG" if VERBOSE_MODE else "INFO").upper()  # 日志级别

def setup_utf8_encoding():
    r"""强制设置 UTF-8 编码，解决日志中 \uxxx 编码问题"""
    # Set environment variables for UTF-8
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONLEGACYWINDOWSSTDIO'] = '0'

    # Force UTF-8 encoding for standard streams
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'reconfigure'):
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    if hasattr(sys.stdin, 'reconfigure'):
        sys.stdin.reconfigure(encoding='utf-8', errors='replace')

    # For Windows, also set console code page
    if sys.platform.startswith('win'):
        try:
            import subprocess
            subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
        except:
            pass

# Set UTF-8 encoding for the entire application
setup_utf8_encoding()

# Configure logging with UTF-8 encoding
log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s' if VERBOSE_MODE else '%(levelname)s: %(message)s'

# Create a custom formatter that ensures UTF-8 encoding
class UTF8Formatter(logging.Formatter):
    def format(self, record):
        # Ensure the message is properly encoded as UTF-8
        formatted = super().format(record)
        if isinstance(formatted, bytes):
            formatted = formatted.decode('utf-8', errors='replace')
        return formatted

# Configure logging with UTF-8 support
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=log_format,
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Apply UTF-8 formatter to all handlers
for handler in logging.root.handlers:
    handler.setFormatter(UTF8Formatter(log_format))
    if hasattr(handler, 'stream') and hasattr(handler.stream, 'reconfigure'):
        try:
            handler.stream.reconfigure(encoding='utf-8')
        except:
            pass

logger = logging.getLogger(__name__)

def safe_print(message: str):
    """安全的 UTF-8 打印函数，确保中文字符正确显示"""
    try:
        # 确保消息是字符串类型
        if not isinstance(message, str):
            message = str(message)

        # 直接打印，依赖之前设置的 UTF-8 编码
        print(message, flush=True)
    except UnicodeEncodeError:
        # 如果仍然有编码问题，使用 errors='replace' 处理
        try:
            print(message.encode('utf-8', errors='replace').decode('utf-8'), flush=True)
        except:
            # 最后的备选方案
            print(repr(message), flush=True)

# 启动时显示配置信息
if VERBOSE_MODE:
    safe_print("=" * 60)
    safe_print("🚀 FastAPI Chat Service 启动配置")
    safe_print("=" * 60)
    safe_print(f"📊 详细模式: {'开启' if VERBOSE_MODE else '关闭'}")
    safe_print(f"🔍 显示错误堆栈: {'开启' if SHOW_TRACEBACK else '关闭'}")
    safe_print(f"📝 显示请求详情: {'开启' if SHOW_REQUEST_DETAILS else '关闭'}")
    safe_print(f"📤 显示响应详情: {'开启' if SHOW_RESPONSE_DETAILS else '关闭'}")
    safe_print(f"📋 日志级别: {LOG_LEVEL}")
    safe_print("=" * 60)
else:
    safe_print("🚀 FastAPI Chat Service 启动中...")

def log_info(message: str, force_print: bool = False):
    """根据配置决定是否显示信息"""
    if VERBOSE_MODE or force_print:
        logger.info(message)
        safe_print(f"ℹ️  {message}")

def log_debug(message: str):
    """根据配置决定是否显示调试信息"""
    if VERBOSE_MODE:
        logger.debug(message)

def log_error(message: str, exc: Exception = None, force_print: bool = True):
    """根据配置决定是否显示错误信息和堆栈"""
    if force_print or VERBOSE_MODE:
        logger.error(message)
        safe_print(f"❌ {message}")

    if exc and SHOW_TRACEBACK:
        traceback_str = traceback.format_exc()
        logger.error(f"错误堆栈:\n{traceback_str}")
        if VERBOSE_MODE:
            safe_print(f"🔍 错误堆栈:\n{traceback_str}")

def log_success(message: str):
    """显示成功信息"""
    if VERBOSE_MODE:
        logger.info(message)
        safe_print(f"✅ {message}")
    else:
        safe_print(f"✅ {message}")


# Pydantic models for request/response
class Message(BaseModel):
    type: str = Field(..., description="Message type: 'text' or 'image'")
    content: str = Field(..., description="Message content")
    role: str = Field(..., description="Message role: 'user', 'assistant', 'system'")


class ChatRequest(BaseModel):
    messages: List[Message] = Field(..., description="List of messages in the conversation")
    model_name: Optional[str] = Field(None, description="Name of the model to use")
    model_type: Optional[str] = Field(None, description="Type of model: 'ollama', 'openai'")
    api_key: Optional[str] = Field(None, description="API key for model authentication")
    openai_base_url: Optional[str] = Field(None, description="Base URL for the model API")
    stream: bool = Field(True, description="Whether to stream the response")
    mcp_url_list: List[str] = Field(default=[], description="List of MCP server URLs")
    team_id: Optional[int] = Field(None, description="Team ID for team-based chat")
    ollama_think: bool = Field(True, description="Whether to enable thinking mode for Ollama models")


class ChatResponse(BaseModel):
    type: str = Field(..., description="Response type")
    content: str = Field(..., description="Response content")
    source: Optional[str] = Field(None, description="Response source")


# Initialize FastAPI app with proper encoding support
app = FastAPI(
    title="AutoGen Studio Chat API",
    description="FastAPI wrapper for AutoGen Studio chat backend",
    version="1.0.0"
)

# Add CORS middleware to allow cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境中应该指定具体的域名
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

# Add middleware to ensure proper UTF-8 handling
@app.middleware("http")
async def add_utf8_header(request: Request, call_next):
    """Middleware to ensure UTF-8 encoding in responses"""
    response = await call_next(request)
    if response.headers.get("content-type", "").startswith("application/json"):
        response.headers["content-type"] = "application/json; charset=utf-8"
    return response


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AutoGen Studio Chat API",
        "version": "1.0.0",
        "endpoints": {
            "chat": "/chat",
            "chat_stream": "/chat/stream",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "autogen-studio-chat-api"}


@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    """
    Non-streaming chat endpoint

    Args:
        request: ChatRequest containing messages and configuration

    Returns:
        ChatResponse with the model's response
    """
    try:
        log_info(f"📨 收到聊天请求: model_name={request.model_name}, model_type={request.model_type}")

        if SHOW_REQUEST_DETAILS:
            log_debug(f"📋 请求详情: {request.model_dump()}")

        # Convert Pydantic messages to dict format expected by chat_backend
        messages_dict = [
            {
                "type": msg.type,
                "content": msg.content,
                "role": msg.role
            }
            for msg in request.messages
        ]

        log_debug(f"🔄 转换后的消息: {messages_dict}")

        # Call chat_backend with stream=False
        log_info("🤖 调用 chat_backend...")
        result_generator = chat_backend(
            messages=messages_dict,
            ollama_think=request.ollama_think,
            model_name=request.model_name,
            model_type=request.model_type,
            api_key=request.api_key,
            openai_base_url=request.openai_base_url,
            stream=False,
            MCP_url_list=request.mcp_url_list,
            team_id=request.team_id
        )

        # Since chat_backend always returns a generator, we need to collect the results
        results = []
        async for item in result_generator:
            results.append(item)

        if SHOW_RESPONSE_DETAILS:
            log_debug(f"🔍 chat_backend 结果: {results}")

        # Handle the results - could be a list of messages or a single result
        if results:
            if len(results) == 1 and isinstance(results[0], list):
                # Team mode returns a list of messages - return the raw list as JSON
                import json

                def serialize_message(msg):
                    """Convert message object to JSON-serializable format"""
                    if isinstance(msg, dict):
                        serialized = {}
                        for key, value in msg.items():
                            if key == 'content':
                                # Handle content field specially
                                if isinstance(value, list):
                                    # For tool calls, convert to string representation
                                    serialized[key] = [str(item) for item in value]
                                else:
                                    serialized[key] = str(value) if value is not None else ""
                            elif key in ['created_at', 'models_usage']:
                                # Convert complex objects to string
                                serialized[key] = str(value) if value is not None else None
                            else:
                                serialized[key] = value
                        return serialized
                    else:
                        return str(msg)

                # Serialize all messages
                serialized_messages = [serialize_message(msg) for msg in results[0]]

                response = ChatResponse(
                    type="team_result",
                    content=json.dumps(serialized_messages, ensure_ascii=False, indent=2),
                    source="team"
                )
                log_success("👥 团队模式聊天请求处理完成")
            elif isinstance(results[0], dict):
                # Single message result
                content = results[0].get("content", "")
                # Handle case where content might be a list (e.g., tool calls)
                if isinstance(content, list):
                    content_str = "\n".join(str(item) for item in content)
                else:
                    content_str = str(content)

                response = ChatResponse(
                    type=results[0].get("type", "result"),
                    content=content_str,
                    source=results[0].get("source")
                )
                log_success("💬 单条消息聊天请求处理完成")
            else:
                # Fallback for other result types
                response = ChatResponse(
                    type="result",
                    content=str(results[0]),
                    source=None
                )
                log_success("📝 聊天请求处理完成")
        else:
            response = ChatResponse(
                type="error",
                content="No response received from chat backend",
                source=None
            )
            log_error("⚠️  未从 chat backend 收到响应")

        return response

    except Exception as e:
        log_error(f"💥 聊天处理失败: {str(e)}", e)
        raise HTTPException(status_code=500, detail=f"Chat processing failed: {str(e)}")


@app.post("/chat/stream")
async def chat_stream_endpoint(request: ChatRequest):
    """
    Streaming chat endpoint

    Args:
        request: ChatRequest containing messages and configuration

    Returns:
        StreamingResponse with Server-Sent Events
    """
    try:
        log_info(f"🌊 收到流式聊天请求: model_name={request.model_name}, model_type={request.model_type}")

        if SHOW_REQUEST_DETAILS:
            log_debug(f"📋 流式请求详情: {request.model_dump()}")

        # Convert Pydantic messages to dict format expected by chat_backend
        messages_dict = [
            {
                "type": msg.type,
                "content": msg.content,
                "role": msg.role
            }
            for msg in request.messages
        ]

        async def generate_stream():
            """Generator function for streaming response with proper UTF-8 encoding"""
            try:
                log_info("🚀 开始流式响应生成...")
                chunk_count = 0

                # Call chat_backend with stream=True
                async for chunk in chat_backend(
                    messages=messages_dict,
                    ollama_think=request.ollama_think,
                    model_name=request.model_name,
                    model_type=request.model_type,
                    api_key=request.api_key,
                    openai_base_url=request.openai_base_url,
                    stream=True,
                    MCP_url_list=request.mcp_url_list,
                    team_id=request.team_id
                ):
                    chunk_count += 1
                    if VERBOSE_MODE and chunk_count % 10 == 0:  # 每10个chunk显示一次进度
                        log_debug(f"📦 已处理 {chunk_count} 个数据块")
                    # Format as Server-Sent Events with proper UTF-8 encoding
                    if isinstance(chunk, dict):
                        # Use safe JSON serialization for dict chunks with proper UTF-8 encoding
                        try:
                            data = json.dumps(chunk, default=str, ensure_ascii=False)
                            yield f"data: {data}\n\n"
                        except (TypeError, ValueError) as e:
                            # If serialization fails, convert to string representation
                            data = json.dumps({"type": "message", "content": str(chunk)}, ensure_ascii=False)
                            yield f"data: {data}\n\n"
                            if VERBOSE_MODE:
                                log_debug(f"⚠️  JSON序列化失败，使用字符串表示: {e}")
                    else:
                        # Check if chunk has dump() method (autogen message object)
                        if hasattr(chunk, 'dump'):
                            try:
                                data = json.dumps(chunk.dump(), ensure_ascii=False)
                                yield f"data: {data}\n\n"
                            except (TypeError, ValueError) as e:
                                # Fallback to string representation
                                data = json.dumps({"type": "message", "content": str(chunk)}, ensure_ascii=False)
                                yield f"data: {data}\n\n"
                                if VERBOSE_MODE:
                                    log_debug(f"⚠️  对象dump失败，使用字符串表示: {e}")
                        else:
                            data = json.dumps({"type": "message", "content": str(chunk)}, ensure_ascii=False)
                            yield f"data: {data}\n\n"

                # Send end-of-stream marker
                log_success(f"🏁 流式响应完成，共处理 {chunk_count} 个数据块")
                yield f"data: {json.dumps({'type': 'end'}, ensure_ascii=False)}\n\n"

            except Exception as e:
                log_error(f"💥 流式处理失败: {str(e)}", e)
                error_data = json.dumps({
                    "type": "error",
                    "content": f"Stream processing failed: {str(e)}"
                }, ensure_ascii=False)
                yield f"data: {error_data}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream; charset=utf-8",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream; charset=utf-8"
            }
        )

    except Exception as e:
        log_error(f"💥 流式设置失败: {str(e)}", e)
        raise HTTPException(status_code=500, detail=f"Stream setup failed: {str(e)}")


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    log_error(f"🚨 未处理的异常 {request.method} {request.url}: {str(exc)}", exc)

    error_content = {
        "error": "Internal server error",
        "detail": str(exc),
        "path": str(request.url)
    }

    # 只在显示堆栈模式下包含traceback
    if SHOW_TRACEBACK:
        error_content["traceback"] = traceback.format_exc().split('\n')

    return JSONResponse(
        status_code=500,
        content=error_content
    )


def run_server(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """
    Run the FastAPI server

    Args:
        host: Host to bind to
        port: Port to bind to
        reload: Enable auto-reload for development
    """

    # 显示服务器启动信息
    if VERBOSE_MODE:
        safe_print("=" * 60)
        safe_print("🌐 服务器启动信息")
        safe_print("=" * 60)
        safe_print(f"🏠 主机地址: {host}")
        safe_print(f"🔌 端口号: {port}")
        safe_print(f"🔄 自动重载: {'开启' if reload else '关闭'}")
        safe_print(f"📊 日志级别: {LOG_LEVEL.lower()}")
        safe_print(f"📝 访问日志: 开启")
        safe_print("=" * 60)
        safe_print(f"🚀 服务器将在 http://{host}:{port} 启动")
        safe_print("📚 API文档: http://{}:{}/docs".format(host if host != "0.0.0.0" else "localhost", port))
        safe_print("=" * 60)
    else:
        safe_print(f"🚀 服务器启动在 http://{host}:{port}")
        safe_print(f"📚 API文档: http://{'localhost' if host == '0.0.0.0' else host}:{port}/docs")

    # 根据详细模式设置uvicorn日志级别
    uvicorn_log_level = LOG_LEVEL.lower() if VERBOSE_MODE else "info"

    # Configure uvicorn logging with UTF-8 encoding
    log_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": "%(levelprefix)s %(message)s",
                "use_colors": None,
            },
            "access": {
                "()": "uvicorn.logging.AccessFormatter",
                "fmt": '%(levelprefix)s %(client_addr)s - "%(request_line)s" %(status_code)s',
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
            "access": {
                "formatter": "access",
                "class": "logging.StreamHandler",
                "stream": "ext://sys.stdout",
            },
        },
        "loggers": {
            "uvicorn": {"handlers": ["default"], "level": uvicorn_log_level.upper()},
            "uvicorn.error": {"level": uvicorn_log_level.upper()},
            "uvicorn.access": {"handlers": ["access"], "level": uvicorn_log_level.upper(), "propagate": False},
        },
    }

    uvicorn.run(
        "fastapi_chat_service:app",
        host=host,
        port=port,
        reload=reload,
        log_level=uvicorn_log_level,
        log_config=log_config,
        access_log=VERBOSE_MODE,  # 根据详细模式决定是否显示访问日志
        # Ensure proper encoding handling
        loop="asyncio",
        http="httptools"
    )


if __name__ == "__main__":
    # 显示环境变量提示
    if not VERBOSE_MODE:
        safe_print("💡 提示: 设置环境变量 VERBOSE_MODE=true 可显示详细信息")
        safe_print("💡 提示: 设置环境变量 SHOW_TRACEBACK=false 可隐藏错误堆栈")

    # Run the server
    run_server(host="0.0.0.0", port=1113, reload=True)
